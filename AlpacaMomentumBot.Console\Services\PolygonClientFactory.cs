using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Services;

public sealed class PolygonClientFactory : IPolygonClientFactory
{
    private readonly HttpClient _client;
    private readonly PolygonRateLimitHelper _rateLimitHelper;

    public PolygonClientFactory(IHttpClientFactory httpFactory, IConfiguration config, ILogger<PolygonRateLimitHelper> rateLimitLogger)
    {
        _client = httpFactory.CreateClient("polygon");
        _client.BaseAddress = new Uri("https://api.polygon.io/");

        var apiKey = config["POLY_API_KEY"];
        if (!string.IsNullOrEmpty(apiKey))
        {
            _client.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
        }

        _rateLimitHelper = new PolygonRateLimitHelper(rateLimitLogger);
    }

    public HttpClient CreateClient() => _client;

    public IPolygonRateLimitHelper GetRateLimitHelper() => _rateLimitHelper;

    public void Dispose()
    {
        _rateLimitHelper?.Dispose();
        _client?.Dispose();
    }
}
