using Polly;

namespace AlpacaMomentumBot.Services;

/// <summary>
/// Factory for creating rate limiting and retry policies for API clients
/// </summary>
public interface IRateLimitPolicyFactory
{
    /// <summary>
    /// Creates a rate limiting policy for Alpaca API (200 requests/minute)
    /// with exponential back-off retry policy
    /// </summary>
    IAsyncPolicy<HttpResponseMessage> CreateAlpacaPolicy();

    /// <summary>
    /// Creates a rate limiting policy for Polygon API (5 requests/second)
    /// with exponential back-off retry policy
    /// </summary>
    IAsyncPolicy<HttpResponseMessage> CreatePolygonPolicy();
}
