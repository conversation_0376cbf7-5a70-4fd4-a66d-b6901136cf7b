using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using Polly;

namespace AlpacaMomentumBot.Services;

/// <summary>
/// Interface for Alpaca rate limiting helper
/// </summary>
public interface IAlpacaRateLimitHelper
{
    /// <summary>
    /// Executes an Alpaca API call with rate limiting and retry logic
    /// </summary>
    Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName = "Unknown");
}

/// <summary>
/// Rate limiting helper for Alpaca API calls with exponential back-off
/// Handles 200 requests/minute limit with intelligent retry strategies
/// </summary>
public sealed class AlpacaRateLimitHelper : IAlpacaRateLimitHelper
{
    private readonly ILogger<AlpacaRateLimitHelper> _logger;
    private readonly IAsyncPolicy _retryPolicy;
    private readonly SemaphoreSlim _semaphore;
    private readonly Timer _resetTimer;
    private int _requestCount;
    private DateTime _windowStart;

    public AlpacaRateLimitHelper(ILogger<AlpacaRateLimitHelper> logger)
    {
        _logger = logger;
        _semaphore = new SemaphoreSlim(1, 1);
        _requestCount = 0;
        _windowStart = DateTime.UtcNow;

        // Reset counter every minute
        _resetTimer = new Timer(ResetCounter, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

        // Exponential back-off retry policy
        _retryPolicy = Policy
            .Handle<Exception>(ex => 
                ex.Message.Contains("TooManyRequests") || 
                ex.Message.Contains("429") ||
                ex.Message.Contains("rate limit", StringComparison.OrdinalIgnoreCase))
            .WaitAndRetryAsync(
                retryCount: 5,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)),
                onRetry: (exception, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Alpaca rate limit retry {RetryCount}/5 after {Delay}ms. Exception: {Exception}",
                        retryCount, timespan.TotalMilliseconds, exception.Message);
                });
    }

    /// <summary>
    /// Executes an Alpaca API call with rate limiting and retry logic
    /// </summary>
    public async Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName = "Unknown")
    {
        await _semaphore.WaitAsync();
        try
        {
            // Check if we need to wait due to rate limiting
            await WaitIfNecessary();

            // Increment request counter
            Interlocked.Increment(ref _requestCount);

            _logger.LogDebug("Executing Alpaca API call: {Operation} (Request {Count}/200 in current window)",
                operationName, _requestCount);

            // Execute with retry policy
            return await _retryPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    return await apiCall();
                }
                catch (Exception ex) when (IsRateLimitException(ex))
                {
                    _logger.LogWarning("Alpaca rate limit hit for {Operation}, will retry with exponential back-off", operationName);
                    throw;
                }
            });
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// Executes an Alpaca API call without return value with rate limiting and retry logic
    /// </summary>
    public async Task ExecuteAsync(Func<Task> apiCall, string operationName = "Unknown")
    {
        await ExecuteAsync(async () =>
        {
            await apiCall();
            return true; // Dummy return value
        }, operationName);
    }

    private async Task WaitIfNecessary()
    {
        // If we're approaching the limit (190/200), add a small delay
        if (_requestCount >= 190)
        {
            var delay = TimeSpan.FromMilliseconds(300 + Random.Shared.Next(0, 200));
            _logger.LogDebug("Approaching Alpaca rate limit ({Count}/200), adding {Delay}ms delay", _requestCount, delay.TotalMilliseconds);
            await Task.Delay(delay);
        }
    }

    private void ResetCounter(object? state)
    {
        var oldCount = Interlocked.Exchange(ref _requestCount, 0);
        _windowStart = DateTime.UtcNow;
        
        if (oldCount > 0)
        {
            _logger.LogDebug("Reset Alpaca rate limit counter. Previous window: {Count}/200 requests", oldCount);
        }
    }

    private static bool IsRateLimitException(Exception ex)
    {
        return ex.Message.Contains("TooManyRequests") ||
               ex.Message.Contains("429") ||
               ex.Message.Contains("rate limit", StringComparison.OrdinalIgnoreCase) ||
               ex.Message.Contains("throttle", StringComparison.OrdinalIgnoreCase);
    }

    public void Dispose()
    {
        _resetTimer?.Dispose();
        _semaphore?.Dispose();
    }
}
