using AlpacaMomentumBot.Console.Extensions;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Services;

public sealed class PortfolioGate : IPortfolioGate
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly ILogger<PortfolioGate> _logger;

    public PortfolioGate(IAlpacaClientFactory clientFactory, ILogger<PortfolioGate> logger)
    {
        _clientFactory = clientFactory;
        _logger = logger;
    }

    public async Task<bool> ShouldTradeAsync()
    {
        try
        {
            using var dataClient = _clientFactory.CreateDataClient();
            
            // Get 250 days of SPY data for SMA200 calculation
            var request = new HistoricalBarsRequest("SPY", BarTimeFrame.Day,
                new Interval<DateTime>(DateTime.UtcNow.AddDays(-300), DateTime.UtcNow));

            var response = await dataClient.ListHistoricalBarsAsync(request);
            var bars = response.Items.ToList();

            if (bars.Count < 200)
            {
                _logger.LogWarning("Insufficient SPY data for SMA200 calculation. Got {Count} bars, need at least 200", bars.Count);
                return false;
            }

            var sma200 = bars.GetSma200();
            var currentPrice = bars.Last().Close;

            var shouldTrade = currentPrice > (decimal)sma200;
            
            _logger.LogInformation("SPY SMA200 check: Current={Current:C}, SMA200={SMA200:C}, ShouldTrade={ShouldTrade}", 
                currentPrice, (decimal)sma200, shouldTrade);

            return shouldTrade;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking SPY SMA200 condition");
            return false;
        }
    }
}
