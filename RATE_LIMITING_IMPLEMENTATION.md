# Rate Limiting Implementation with Exponential Back-off

## Overview

This document describes the implementation of rate limiting helpers for both Alpaca and Polygon APIs with exponential back-off policies. The implementation ensures compliance with API rate limits while providing resilient retry mechanisms for transient failures.

## Rate Limits

### Alpaca Markets
- **Limit**: 200 requests per minute
- **Strategy**: Conservative approach with 300ms base delay between requests
- **Retry Policy**: Exponential back-off with jitter (up to 5 retries)

### Polygon.io (Starter Plan)
- **Limit**: 5 requests per second
- **Strategy**: Conservative approach with 250ms base delay between requests
- **Retry Policy**: Exponential back-off with jitter (up to 5 retries)

## Architecture

### Core Components

#### 1. IRateLimitPolicyFactory & RateLimitPolicyFactory
- **Purpose**: Creates Polly policies for HTTP-level rate limiting and retry logic
- **Location**: `AlpacaMomentumBot.Console/Services/`
- **Features**:
  - Alpaca policy: 200 req/min with exponential back-off
  - Polygon policy: 5 req/sec with exponential back-off
  - Handles HTTP status codes: 429 (Too Many Requests), 503 (Service Unavailable), 408 (Request Timeout)

#### 2. AlpacaRateLimitHelper
- **Purpose**: Application-level rate limiting for Alpaca API calls
- **Location**: `AlpacaMomentumBot.Console/Services/`
- **Features**:
  - Thread-safe request counting with SemaphoreSlim
  - Automatic counter reset every minute
  - Proactive delays when approaching limits (190/200 requests)
  - Exponential back-off retry policy for rate limit exceptions

#### 3. PolygonRateLimitHelper
- **Purpose**: Application-level rate limiting for Polygon API calls
- **Location**: `AlpacaMomentumBot.Console/Services/`
- **Features**:
  - Thread-safe request counting with SemaphoreSlim
  - Automatic counter reset every second
  - Proactive delays when approaching limits (4/5 and 5/5 requests)
  - Support for both HTTP and generic API calls

### Updated Client Factories

#### AlpacaClientFactory
- **Enhanced**: Now includes AlpacaRateLimitHelper instance
- **Interface**: Updated IAlpacaClientFactory to include GetRateLimitHelper() method
- **Disposal**: Implements proper resource cleanup

#### PolygonClientFactory
- **Enhanced**: Now includes PolygonRateLimitHelper instance
- **HTTP Client**: Configured with Polly policies via IHttpClientFactory
- **Interface**: Updated IPolygonClientFactory to include GetRateLimitHelper() method

## Implementation Details

### Exponential Back-off Formula

```csharp
TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000))
```

- **Base Delay**: 2^retryAttempt seconds
- **Jitter**: Random 0-1000ms to prevent thundering herd
- **Max Retries**: 5 attempts

### Rate Limiting Logic

#### Alpaca (200 req/min)
```csharp
// Proactive delay when approaching limit
if (_requestCount >= 190)
{
    var delay = TimeSpan.FromMilliseconds(300 + Random.Shared.Next(0, 200));
    await Task.Delay(delay);
}
```

#### Polygon (5 req/sec)
```csharp
// Proactive delay at limit
if (_requestCount >= 5)
{
    var delay = TimeSpan.FromMilliseconds(200 + Random.Shared.Next(0, 100));
    await Task.Delay(delay);
}
// Proactive delay approaching limit
else if (_requestCount >= 4)
{
    var delay = TimeSpan.FromMilliseconds(100 + Random.Shared.Next(0, 50));
    await Task.Delay(delay);
}
```

## Usage Examples

### MarketDataService Integration

```csharp
public async Task<IPage<IBar>> GetStockBarsAsync(string symbol, DateTime startDate, DateTime endDate)
{
    var rateLimitHelper = _alpacaFactory.GetRateLimitHelper();
    
    return await rateLimitHelper.ExecuteAsync(async () =>
    {
        using var dataClient = _alpacaFactory.CreateDataClient();
        var request = new HistoricalBarsRequest(symbol, BarTimeFrame.Day,
            new Interval<DateTime>(startDate, endDate));
        return await dataClient.ListHistoricalBarsAsync(request);
    }, $"GetStockBars-{symbol}");
}
```

### Polygon API Integration

```csharp
public async Task<decimal?> GetIndexValueAsync(string indexSymbol)
{
    var rateLimitHelper = _polygonFactory.GetRateLimitHelper();
    
    return await rateLimitHelper.ExecuteAsync(async () =>
    {
        using var httpClient = _polygonFactory.CreateClient();
        var url = $"v2/last/trade/{indexSymbol}";
        var response = await httpClient.GetAsync(url);
        // Process response...
    }, $"GetIndexValue-{indexSymbol}");
}
```

## Configuration

### Program.cs Setup

```csharp
// Rate limiting policies
services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();

// Configure Polygon HTTP client with Polly policies
services.AddHttpClient("polygon", client =>
{
    client.BaseAddress = new Uri("https://api.polygon.io/");
    client.Timeout = TimeSpan.FromSeconds(30);
})
.AddPolicyHandler((serviceProvider, request) =>
{
    var policyFactory = serviceProvider.GetRequiredService<IRateLimitPolicyFactory>();
    return policyFactory.CreatePolygonPolicy();
});
```

## Testing

### Unit Tests Coverage

1. **RateLimitPolicyFactoryTests**
   - Policy creation validation
   - Retry behavior on HTTP errors
   - Success scenarios

2. **AlpacaRateLimitHelperTests**
   - Successful operation execution
   - Retry on rate limit exceptions
   - Concurrent request handling
   - Resource disposal

3. **PolygonRateLimitHelperTests**
   - HTTP and generic call execution
   - Retry mechanisms
   - Concurrent request handling
   - Resource disposal

## Benefits

### Reliability
- **Automatic Retries**: Handles transient failures gracefully
- **Rate Limit Compliance**: Prevents API quota exhaustion
- **Fallback Mechanisms**: Polygon fallback for Alpaca throttling

### Performance
- **Proactive Delays**: Prevents hitting rate limits
- **Concurrent Safety**: Thread-safe request counting
- **Efficient Batching**: Maintains existing batch processing logic

### Observability
- **Comprehensive Logging**: Detailed retry and rate limit logging
- **Operation Naming**: Contextual operation identification
- **Performance Metrics**: Request counting and timing

## Monitoring

### Log Messages
- Rate limit warnings with retry counts
- Request counter resets
- Proactive delay notifications
- Exception details with context

### Key Metrics to Monitor
- Request counts per time window
- Retry frequencies
- Average response times
- Rate limit hit frequencies

## Future Enhancements

1. **Metrics Collection**: Add Prometheus/OpenTelemetry metrics
2. **Dynamic Rate Limits**: Adjust limits based on API responses
3. **Circuit Breaker**: Implement circuit breaker pattern for persistent failures
4. **Rate Limit Headers**: Parse and respect API rate limit headers
5. **Adaptive Delays**: Machine learning-based delay optimization
