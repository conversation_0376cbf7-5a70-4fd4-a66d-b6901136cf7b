using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Services;

public sealed class TradeExecutor : ITradeExecutor
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly ILogger<TradeExecutor> _logger;

    public TradeExecutor(IAlpacaClientFactory clientFactory, ILogger<TradeExecutor> logger)
    {
        _clientFactory = clientFactory;
        _logger = logger;
    }

    public async Task ExecuteTradeAsync(TradingSignal signal, decimal quantity)
    {
        if (quantity <= 0)
        {
            _logger.LogWarning("Invalid quantity {Quantity} for {Symbol}, skipping trade", quantity, signal.Symbol);
            return;
        }

        var rateLimitHelper = _clientFactory.GetRateLimitHelper();

        await rateLimitHelper.ExecuteAsync<object>(async () =>
        {
            try
            {
                using var tradingClient = _clientFactory.CreateTradingClient();

                // Cancel existing orders for this symbol
                await CancelExistingOrdersAsync(tradingClient, signal.Symbol);

                // Calculate entry price: lastClose * 1.002m (Limit-on-Open)
                var entryPrice = signal.Price * 1.002m;

                // Calculate stop-loss price: entry - 2×ATR
                var stopLossPrice = entryPrice - (2m * signal.Atr);

                // For now, just log the trade execution (placeholder implementation)
                _logger.LogInformation("Would execute trade for {Symbol}: Quantity={Quantity}, EntryPrice={EntryPrice:C}, StopPrice={StopPrice:C}",
                    signal.Symbol, quantity, entryPrice, stopLossPrice);

                // TODO: Implement actual order submission once API usage is clarified
                return (object)null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing trade for {Symbol}", signal.Symbol);
                return (object)null;
            }
        }, $"ExecuteTrade-{signal.Symbol}");
    }

    private async Task CancelExistingOrdersAsync(IAlpacaTradingClient tradingClient, string symbol)
    {
        var rateLimitHelper = _clientFactory.GetRateLimitHelper();

        await rateLimitHelper.ExecuteAsync<object>(async () =>
        {
            try
            {
                var openOrders = await tradingClient.ListOrdersAsync(new ListOrdersRequest
                {
                    OrderStatusFilter = OrderStatusFilter.Open,
                    LimitOrderNumber = 100
                });

                var symbolOrders = openOrders.Where(o => o.Symbol == symbol).ToList();

                foreach (var order in symbolOrders)
                {
                    await tradingClient.CancelOrderAsync(order.OrderId);
                    _logger.LogInformation("Cancelled existing order {OrderId} for {Symbol}", order.OrderId, symbol);
                }
                return (object)null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error cancelling existing orders for {Symbol}", symbol);
                return (object)null;
            }
        }, $"CancelOrders-{symbol}");
    }
}
