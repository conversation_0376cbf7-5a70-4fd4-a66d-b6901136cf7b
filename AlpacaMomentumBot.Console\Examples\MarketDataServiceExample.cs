using AlpacaMomentumBot.Services;
using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Console.Examples;

/// <summary>
/// Example demonstrating how to use the unified MarketDataService
/// that combines Alpaca (for stocks/ETFs) and Polygon (for indices) data sources
/// </summary>
public class MarketDataServiceExample
{
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<MarketDataServiceExample> _logger;

    public MarketDataServiceExample(
        IMarketDataService marketDataService,
        ILogger<MarketDataServiceExample> logger)
    {
        _marketDataService = marketDataService;
        _logger = logger;
    }

    /// <summary>
    /// Demonstrates fetching stock data from Alpaca and index data from Polygon
    /// </summary>
    public async Task RunExampleAsync()
    {
        _logger.LogInformation("=== Market Data Service Example ===");

        // Example 1: Get stock data from Alpaca
        await GetStockDataExample();

        // Example 2: Get multiple stocks data from Alpaca
        await GetMultipleStocksExample();

        // Example 3: Get index value from Polygon
        await GetIndexValueExample();

        // Example 4: Get historical index data from Polygon
        await GetIndexHistoryExample();

        _logger.LogInformation("=== Example completed ===");
    }

    private async Task GetStockDataExample()
    {
        try
        {
            _logger.LogInformation("--- Getting AAPL stock data from Alpaca ---");
            
            var startDate = DateTime.UtcNow.AddDays(-30);
            var endDate = DateTime.UtcNow;
            
            var bars = await _marketDataService.GetStockBarsAsync("AAPL", startDate, endDate);
            var barsList = bars.Items.ToList();
            
            if (barsList.Any())
            {
                var latestBar = barsList.Last();
                _logger.LogInformation("AAPL latest price: ${Price:F2} (from {Date})", 
                    latestBar.Close, latestBar.TimeUtc.ToString("yyyy-MM-dd"));
                _logger.LogInformation("Retrieved {Count} bars for AAPL", barsList.Count);
            }
            else
            {
                _logger.LogWarning("No AAPL data retrieved");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting AAPL stock data");
        }
    }

    private async Task GetMultipleStocksExample()
    {
        try
        {
            _logger.LogInformation("--- Getting multiple stocks data from Alpaca ---");
            
            var symbols = new[] { "SPY", "QQQ", "MSFT" };
            var startDate = DateTime.UtcNow.AddDays(-7);
            var endDate = DateTime.UtcNow;
            
            var allBars = await _marketDataService.GetStockBarsAsync(symbols, startDate, endDate);
            
            foreach (var kvp in allBars)
            {
                var symbol = kvp.Key;
                var bars = kvp.Value.Items.ToList();
                
                if (bars.Any())
                {
                    var latestPrice = bars.Last().Close;
                    _logger.LogInformation("{Symbol}: ${Price:F2} ({Count} bars)", 
                        symbol, latestPrice, bars.Count);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting multiple stocks data");
        }
    }

    private async Task GetIndexValueExample()
    {
        try
        {
            _logger.LogInformation("--- Getting SPX index value from Polygon ---");
            
            var spxValue = await _marketDataService.GetIndexValueAsync("I:SPX");
            
            if (spxValue.HasValue)
            {
                _logger.LogInformation("S&P 500 Index (SPX): {Value:F2}", spxValue.Value);
            }
            else
            {
                _logger.LogWarning("Could not retrieve SPX index value");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SPX index value");
        }
    }

    private async Task GetIndexHistoryExample()
    {
        try
        {
            _logger.LogInformation("--- Getting VIX historical data from Polygon ---");
            
            var startDate = DateTime.UtcNow.AddDays(-10);
            var endDate = DateTime.UtcNow;
            
            var vixBars = await _marketDataService.GetIndexBarsAsync("I:VIX", startDate, endDate);
            var vixBarsList = vixBars.ToList();
            
            if (vixBarsList.Any())
            {
                var latestVix = vixBarsList.Last();
                _logger.LogInformation("VIX latest: {Value:F2} (from {Date})", 
                    latestVix.Close, latestVix.TimeUtc.ToString("yyyy-MM-dd"));
                _logger.LogInformation("Retrieved {Count} VIX bars", vixBarsList.Count);
            }
            else
            {
                _logger.LogWarning("No VIX historical data retrieved");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting VIX historical data");
        }
    }
}
