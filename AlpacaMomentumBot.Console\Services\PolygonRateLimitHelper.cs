using Microsoft.Extensions.Logging;
using Polly;
using System.Net;

namespace AlpacaMomentumBot.Services;

/// <summary>
/// Interface for Polygon rate limiting helper
/// </summary>
public interface IPolygonRateLimitHelper
{
    /// <summary>
    /// Executes a Polygon HTTP API call with rate limiting and retry logic
    /// </summary>
    Task<HttpResponseMessage> ExecuteAsync(Func<Task<HttpResponseMessage>> httpCall, string operationName = "Unknown");

    /// <summary>
    /// Executes a Polygon API call that returns a specific type with rate limiting and retry logic
    /// </summary>
    Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName = "Unknown");
}

/// <summary>
/// Rate limiting helper for Polygon API calls with exponential back-off
/// Handles 5 requests/second limit with intelligent retry strategies
/// </summary>
public sealed class PolygonRateLimitHelper : IPolygonRateLimitHelper
{
    private readonly ILogger<PolygonRateLimitHelper> _logger;
    private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;
    private readonly SemaphoreSlim _semaphore;
    private readonly Timer _resetTimer;
    private int _requestCount;
    private DateTime _windowStart;

    public PolygonRateLimitHelper(ILogger<PolygonRateLimitHelper> logger)
    {
        _logger = logger;
        _semaphore = new SemaphoreSlim(1, 1);
        _requestCount = 0;
        _windowStart = DateTime.UtcNow;

        // Reset counter every second
        _resetTimer = new Timer(ResetCounter, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));

        // Exponential back-off retry policy for HTTP responses
        _retryPolicy = Policy
            .HandleResult<HttpResponseMessage>(r => 
                r.StatusCode == HttpStatusCode.TooManyRequests ||
                r.StatusCode == HttpStatusCode.ServiceUnavailable ||
                r.StatusCode == HttpStatusCode.RequestTimeout)
            .Or<HttpRequestException>()
            .Or<TaskCanceledException>()
            .WaitAndRetryAsync(
                retryCount: 5,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Polygon rate limit retry {RetryCount}/5 after {Delay}ms. Status: {Status}",
                        retryCount, timespan.TotalMilliseconds, 
                        outcome.Result?.StatusCode.ToString() ?? outcome.Exception?.Message ?? "Unknown");
                });
    }

    /// <summary>
    /// Executes a Polygon HTTP API call with rate limiting and retry logic
    /// </summary>
    public async Task<HttpResponseMessage> ExecuteAsync(Func<Task<HttpResponseMessage>> httpCall, string operationName = "Unknown")
    {
        await _semaphore.WaitAsync();
        try
        {
            // Check if we need to wait due to rate limiting
            await WaitIfNecessary();

            // Increment request counter
            Interlocked.Increment(ref _requestCount);

            _logger.LogDebug("Executing Polygon API call: {Operation} (Request {Count}/5 in current window)",
                operationName, _requestCount);

            // Execute with retry policy
            return await _retryPolicy.ExecuteAsync(async () =>
            {
                var response = await httpCall();
                
                if (response.StatusCode == HttpStatusCode.TooManyRequests)
                {
                    _logger.LogWarning("Polygon rate limit hit for {Operation}, will retry with exponential back-off", operationName);
                }
                
                return response;
            });
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// Executes a Polygon API call that returns a specific type with rate limiting and retry logic
    /// </summary>
    public async Task<T> ExecuteAsync<T>(Func<Task<T>> apiCall, string operationName = "Unknown")
    {
        await _semaphore.WaitAsync();
        try
        {
            // Check if we need to wait due to rate limiting
            await WaitIfNecessary();

            // Increment request counter
            Interlocked.Increment(ref _requestCount);

            _logger.LogDebug("Executing Polygon API call: {Operation} (Request {Count}/5 in current window)",
                operationName, _requestCount);

            // For non-HTTP calls, use a simpler retry policy
            var simpleRetryPolicy = Policy
                .Handle<Exception>(ex => 
                    ex.Message.Contains("TooManyRequests") || 
                    ex.Message.Contains("429") ||
                    ex.Message.Contains("rate limit", StringComparison.OrdinalIgnoreCase))
                .WaitAndRetryAsync(
                    retryCount: 5,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)),
                    onRetry: (exception, timespan, retryCount, context) =>
                    {
                        _logger.LogWarning("Polygon rate limit retry {RetryCount}/5 after {Delay}ms. Exception: {Exception}",
                            retryCount, timespan.TotalMilliseconds, exception.Message);
                    });

            return await simpleRetryPolicy.ExecuteAsync(apiCall);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    private async Task WaitIfNecessary()
    {
        // If we're at the limit (5/5), add a delay
        if (_requestCount >= 5)
        {
            var delay = TimeSpan.FromMilliseconds(200 + Random.Shared.Next(0, 100));
            _logger.LogDebug("At Polygon rate limit ({Count}/5), adding {Delay}ms delay", _requestCount, delay.TotalMilliseconds);
            await Task.Delay(delay);
        }
        // If we're approaching the limit (4/5), add a smaller delay
        else if (_requestCount >= 4)
        {
            var delay = TimeSpan.FromMilliseconds(100 + Random.Shared.Next(0, 50));
            _logger.LogDebug("Approaching Polygon rate limit ({Count}/5), adding {Delay}ms delay", _requestCount, delay.TotalMilliseconds);
            await Task.Delay(delay);
        }
    }

    private void ResetCounter(object? state)
    {
        var oldCount = Interlocked.Exchange(ref _requestCount, 0);
        _windowStart = DateTime.UtcNow;
        
        if (oldCount > 0)
        {
            _logger.LogDebug("Reset Polygon rate limit counter. Previous window: {Count}/5 requests", oldCount);
        }
    }

    public void Dispose()
    {
        _resetTimer?.Dispose();
        _semaphore?.Dispose();
    }
}
