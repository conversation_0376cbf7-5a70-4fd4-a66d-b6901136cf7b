using Serilog;

namespace AlpacaMomentumBot.Services;

public sealed class TradingService : ITradingService
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly IRiskManager _riskManager;
    private readonly IPortfolioGate _portfolioGate;
    private readonly ITradeExecutor _executor;

    public TradingService(
        ISignalGenerator signalGenerator,
        IRiskManager riskManager,
        IPortfolioGate portfolioGate,
        ITradeExecutor executor)
    {
        _signalGenerator = signalGenerator;
        _riskManager = riskManager;
        _portfolioGate = portfolioGate;
        _executor = executor;
    }

    public async Task ExecuteCycleAsync(CancellationToken cancellationToken = default)
    {
        // Check if we should trade today (SPY SMA200 check)
        if (!await _portfolioGate.ShouldTradeAsync())
        {
            Log.Information("Portfolio gate blocked trading - SPY below SMA200");
            return;
        }

        // Generate trading signals
        var signals = await _signalGenerator.RunAsync(10);

        foreach (var signal in signals)
        {
            var quantity = await _riskManager.CalculateQuantityAsync(signal);
            if (quantity > 0)
            {
                await _executor.ExecuteTradeAsync(signal, quantity);
            }
        }

        Log.Information("Trading cycle completed");
    }
}
